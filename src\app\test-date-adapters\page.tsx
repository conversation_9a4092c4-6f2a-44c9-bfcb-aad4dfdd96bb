/**
 * 日期适配器测试页面
 */

'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Button } from '@/shared/components/button';
import { Badge } from '@/shared/components/badge';
import {
  useUnifiedDate,
  useDateTimePicker,
  useTaskScheduling,
  useCountdown,
  useCalendar,
  useTimeFormatting,
  useRelativeTime
} from '@/core/adapters/hooks/useUnifiedDate';
import { initializeAdapters } from '@/core/adapters/AdapterRegistry';
import { AdapterManager, AdapterType, DateLibrary } from '@/core/adapters/AdapterManager';

interface TestResult {
  name: string;
  dayjsResult: string;
  dateFnsResult: string;
  isConsistent: boolean;
  error?: string;
}

export default function TestDateAdaptersPage() {
  const [status, setStatus] = useState<string>('初始化中...');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentLibrary, setCurrentLibrary] = useState<DateLibrary>(DateLibrary.DAYJS);
  const [isInitialized, setIsInitialized] = useState(false);

  // 先初始化适配器系统
  useEffect(() => {
    try {
      console.log('🚀 初始化日期适配器系统...');
      initializeAdapters();

      // 检查适配器是否注册成功
      const adapterManager = AdapterManager.getInstance();
      const registeredAdapters = adapterManager.getRegisteredAdapters();
      console.log('📋 已注册的适配器:', registeredAdapters);

      const activeAdapters = adapterManager.getActiveAdapters();
      console.log('🎯 活跃适配器:', activeAdapters);

      setIsInitialized(true);
      setStatus('✅ 系统初始化成功');
    } catch (error) {
      console.error('❌ 系统初始化失败:', error);
      setStatus('❌ 初始化失败: ' + (error as Error).message);
    }
  }, []);

  // 获取日期方法（使用 try-catch 处理可能的错误）
  let dateTimePicker, taskScheduling, countdown, calendar, timeFormatting, relativeTime, unifiedDate;

  try {
    dateTimePicker = useDateTimePicker();
    taskScheduling = useTaskScheduling();
    countdown = useCountdown();
    calendar = useCalendar();
    timeFormatting = useTimeFormatting();
    relativeTime = useRelativeTime();
    unifiedDate = useUnifiedDate();
  } catch (error) {
    console.warn('Hook 调用失败，可能是适配器未初始化:', error);
  }

  // 运行基础测试
  useEffect(() => {
    if (isInitialized && unifiedDate) {
      runBasicTests();
    }
  }, [isInitialized]);

  const runBasicTests = async () => {
    if (!unifiedDate || !relativeTime) {
      console.warn('适配器未初始化，跳过测试');
      return;
    }

    const results: TestResult[] = [];
    const testDate = '2024-01-15 14:30:00';

    try {
      // 测试格式化
      const formatTest = await testFunction(
        '日期格式化',
        () => unifiedDate.format(testDate, 'YYYY-MM-DD HH:mm:ss'),
        () => unifiedDate.format(testDate, 'YYYY-MM-DD HH:mm:ss')
      );
      results.push(formatTest);

      // 测试日期计算
      const addTest = await testFunction(
        '日期加法',
        () => unifiedDate.add(testDate, 7, 'day').format('YYYY-MM-DD'),
        () => unifiedDate.add(testDate, 7, 'day').format('YYYY-MM-DD')
      );
      results.push(addTest);

      // 测试日期比较
      const compareTest = await testFunction(
        '日期比较',
        () => unifiedDate.isBefore(testDate, new Date()).toString(),
        () => unifiedDate.isBefore(testDate, new Date()).toString()
      );
      results.push(compareTest);

      // 测试相对时间
      const relativeTest = await testFunction(
        '相对时间',
        () => relativeTime.formatDistance(testDate),
        () => relativeTime.formatDistance(testDate)
      );
      results.push(relativeTest);

      setTestResults(results);
    } catch (error) {
      console.error('测试执行失败:', error);
    }
  };

  const testFunction = async (
    name: string,
    dayjsFunc: () => string,
    dateFnsFunc: () => string
  ): Promise<TestResult> => {
    try {
      // 切换到 dayjs
      const adapterManager = AdapterManager.getInstance();
      adapterManager.switchAdapter(AdapterType.DATE, DateLibrary.DAYJS);
      const dayjsResult = dayjsFunc();

      // 切换到 date-fns
      adapterManager.switchAdapter(AdapterType.DATE, DateLibrary.DATE_FNS);
      const dateFnsResult = dateFnsFunc();

      return {
        name,
        dayjsResult,
        dateFnsResult,
        isConsistent: dayjsResult === dateFnsResult,
      };
    } catch (error) {
      return {
        name,
        dayjsResult: 'Error',
        dateFnsResult: 'Error',
        isConsistent: false,
        error: (error as Error).message,
      };
    }
  };

  const switchLibrary = (library: DateLibrary) => {
    try {
      const adapterManager = AdapterManager.getInstance();
      adapterManager.switchAdapter(AdapterType.DATE, library);
      setCurrentLibrary(library);
      console.log(`🔄 已切换到 ${library} 适配器`);
    } catch (error) {
      console.error('切换适配器失败:', error);
    }
  };

  const runPerformanceTest = () => {
    const iterations = 1000;
    const testDate = new Date();

    console.time('Dayjs Performance');
    switchLibrary(DateLibrary.DAYJS);
    for (let i = 0; i < iterations; i++) {
      unifiedDate.format(testDate, 'YYYY-MM-DD HH:mm:ss');
      unifiedDate.add(testDate, i, 'day');
    }
    console.timeEnd('Dayjs Performance');

    console.time('Date-fns Performance');
    switchLibrary(DateLibrary.DATE_FNS);
    for (let i = 0; i < iterations; i++) {
      unifiedDate.format(testDate, 'YYYY-MM-DD HH:mm:ss');
      unifiedDate.add(testDate, i, 'day');
    }
    console.timeEnd('Date-fns Performance');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">📅 日期适配器测试</h1>
        <p className="text-muted-foreground">
          测试和验证 Dayjs 与 Date-fns 适配器的功能一致性
        </p>
      </div>

      {/* 状态显示 */}
      <Card>
        <CardHeader>
          <CardTitle>系统状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <span className={status.includes('✅') ? 'text-green-600' : 'text-red-600'}>
              {status}
            </span>
            <Badge variant={currentLibrary === DateLibrary.DAYJS ? 'default' : 'secondary'}>
              当前库: {currentLibrary}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle>控制面板</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={() => switchLibrary(DateLibrary.DAYJS)}
              variant={currentLibrary === DateLibrary.DAYJS ? 'default' : 'outline'}
            >
              切换到 Dayjs
            </Button>
            <Button
              onClick={() => switchLibrary(DateLibrary.DATE_FNS)}
              variant={currentLibrary === DateLibrary.DATE_FNS ? 'default' : 'outline'}
            >
              切换到 Date-fns
            </Button>
          </div>

          <div className="flex gap-2">
            <Button onClick={runBasicTests} variant="outline">
              运行基础测试
            </Button>
            <Button onClick={runPerformanceTest} variant="outline">
              性能测试
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{result.name}</h4>
                    <Badge variant={result.isConsistent ? 'default' : 'destructive'}>
                      {result.isConsistent ? '✅ 一致' : '❌ 不一致'}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Dayjs 结果:</span>
                      <div className="bg-gray-50 p-2 rounded mt-1">{result.dayjsResult}</div>
                    </div>
                    <div>
                      <span className="font-medium">Date-fns 结果:</span>
                      <div className="bg-gray-50 p-2 rounded mt-1">{result.dateFnsResult}</div>
                    </div>
                  </div>

                  {result.error && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      错误: {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 功能演示 */}
      <Card>
        <CardHeader>
          <CardTitle>功能演示</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">日期时间选择器</h4>
              <div className="text-sm bg-gray-50 p-2 rounded">
                {dateTimePicker ? dateTimePicker.format(new Date(), 'YYYY-MM-DD HH:mm:ss') : '未初始化'}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">任务调度</h4>
              <div className="text-sm bg-gray-50 p-2 rounded">
                {taskScheduling ? taskScheduling.add(new Date(), 1, 'day').format('YYYY-MM-DD') : '未初始化'}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">倒计时</h4>
              <div className="text-sm bg-gray-50 p-2 rounded">
                {countdown ? countdown.formatDistance(new Date(), countdown.add(new Date(), 2, 'hour').toDate()) : '未初始化'}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">日历</h4>
              <div className="text-sm bg-gray-50 p-2 rounded">
                {calendar ? calendar.startOf(new Date(), 'month').format('YYYY-MM-DD') : '未初始化'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
